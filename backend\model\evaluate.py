import os
import numpy as np
import matplotlib.pyplot as plt
import glob
from PIL import Image
import argparse
import json
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix
import seaborn as sns
from tensorflow.keras.models import load_model

def load_test_data(data_dir, img_size=128):
    """
    Tải dữ liệu test từ cả NORMAL và PNEUMONIA

    Args:
        data_dir: Đường dẫn đến thư mục dataset
        img_size: <PERSON><PERSON><PERSON> thước ảnh

    Returns:
        test_images: Mảng ảnh test
        test_labels: Nhãn (0: NORMAL, 1: PNEUMONIA)
        test_paths: Đường dẫn các file ảnh
    """
    # Lấy ảnh NORMAL từ test/NORMAL/
    normal_pattern = os.path.join(data_dir, 'test', 'NORMAL', '*.jpeg')
    normal_images = glob.glob(normal_pattern)

    # Lấy ảnh PNEUMONIA từ test/PNEUMONIA/
    pneumonia_pattern = os.path.join(data_dir, 'test', 'PNEUMONIA', '*.jpeg')
    pneumonia_images = glob.glob(pneumonia_pattern)

    if not normal_images and not pneumonia_images:
        print("Cảnh báo: Không tìm thấy ảnh test!")
        return None, None, None

    # Tải và tiền xử lý ảnh
    test_images = []
    test_labels = []
    test_paths = []

    # Tải ảnh NORMAL (label = 0)
    for img_path in normal_images:
        try:
            img = Image.open(img_path).convert('L')
            img = img.resize((img_size, img_size))
            img_array = np.array(img) / 255.0
            test_images.append(img_array)
            test_labels.append(0)  # NORMAL = 0
            test_paths.append(img_path)
        except Exception as e:
            print(f"Lỗi khi tải {img_path}: {e}")

    # Tải ảnh PNEUMONIA (label = 1)
    for img_path in pneumonia_images:
        try:
            img = Image.open(img_path).convert('L')
            img = img.resize((img_size, img_size))
            img_array = np.array(img) / 255.0
            test_images.append(img_array)
            test_labels.append(1)  # PNEUMONIA = 1
            test_paths.append(img_path)
        except Exception as e:
            print(f"Lỗi khi tải {img_path}: {e}")

    test_images = np.array(test_images)
    test_labels = np.array(test_labels)
    test_images = np.expand_dims(test_images, axis=-1)

    print(f"Tổng số ảnh test: {len(test_images)} (NORMAL: {np.sum(test_labels == 0)}, PNEUMONIA: {np.sum(test_labels == 1)})")

    return test_images, test_labels, test_paths

def calculate_reconstruction_errors(model, images):
    """
    Tính lỗi tái tạo cho từng ảnh

    Args:
        model: Mô hình autoencoder
        images: Mảng ảnh

    Returns:
        reconstruction_errors: Mảng lỗi tái tạo (MSE)
    """
    reconstructions = model.predict(images, verbose=0)
    mse = np.mean(np.square(images - reconstructions), axis=(1, 2, 3))
    return mse

def evaluate_model(model, test_images, test_labels, threshold):
    """
    Đánh giá mô hình trên tập test

    Args:
        model: Mô hình autoencoder
        test_images: Ảnh test
        test_labels: Nhãn thực tế
        threshold: Ngưỡng phát hiện bất thường

    Returns:
        metrics: Dictionary chứa các metrics đánh giá
    """
    # Tính lỗi tái tạo
    reconstruction_errors = calculate_reconstruction_errors(model, test_images)

    # Dự đoán: lỗi > threshold => PNEUMONIA (1), ngược lại => NORMAL (0)
    predictions = (reconstruction_errors > threshold).astype(int)

    # Tính các metrics
    accuracy = accuracy_score(test_labels, predictions)
    precision = precision_score(test_labels, predictions)
    recall = recall_score(test_labels, predictions)
    f1 = f1_score(test_labels, predictions)

    # ROC-AUC sử dụng reconstruction error làm score
    roc_auc = roc_auc_score(test_labels, reconstruction_errors)

    # Confusion Matrix
    cm = confusion_matrix(test_labels, predictions)

    metrics = {
        'accuracy': float(accuracy),
        'precision': float(precision),
        'recall': float(recall),
        'f1_score': float(f1),
        'roc_auc': float(roc_auc),
        'confusion_matrix': cm.tolist(),
        'threshold': float(threshold),
        'total_samples': len(test_labels),
        'normal_samples': int(np.sum(test_labels == 0)),
        'pneumonia_samples': int(np.sum(test_labels == 1)),
        'true_positives': int(cm[1, 1]),
        'true_negatives': int(cm[0, 0]),
        'false_positives': int(cm[0, 1]),
        'false_negatives': int(cm[1, 0])
    }

    return metrics, reconstruction_errors, predictions

def plot_evaluation_results(test_labels, reconstruction_errors, predictions, threshold, output_dir):
    """
    Vẽ biểu đồ kết quả đánh giá
    """
    plt.figure(figsize=(15, 10))

    # 1. Histogram của reconstruction errors
    plt.subplot(2, 3, 1)
    normal_errors = reconstruction_errors[test_labels == 0]
    pneumonia_errors = reconstruction_errors[test_labels == 1]

    plt.hist(normal_errors, bins=50, alpha=0.7, label='Bình thường', color='blue')
    plt.hist(pneumonia_errors, bins=50, alpha=0.7, label='Viêm phổi', color='red')
    plt.axvline(threshold, color='black', linestyle='--', label=f'Ngưỡng: {threshold:.4f}')
    plt.xlabel('Lỗi Tái Tạo (MSE)')
    plt.ylabel('Tần Số')
    plt.title('Phân Bố Lỗi Tái Tạo')
    plt.legend()

    # 2. Confusion Matrix
    plt.subplot(2, 3, 2)
    cm = confusion_matrix(test_labels, predictions)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=['Bình thường', 'Viêm phổi'],
                yticklabels=['Bình thường', 'Viêm phổi'])
    plt.title('Ma Trận Nhầm Lẫn')
    plt.ylabel('Nhãn Thực Tế')
    plt.xlabel('Nhãn Dự Đoán')

    # 3. ROC Curve
    from sklearn.metrics import roc_curve
    fpr, tpr, _ = roc_curve(test_labels, reconstruction_errors)
    plt.subplot(2, 3, 3)
    plt.plot(fpr, tpr, label=f'Đường Cong ROC (AUC = {roc_auc_score(test_labels, reconstruction_errors):.3f})')
    plt.plot([0, 1], [0, 1], 'k--', label='Ngẫu nhiên')
    plt.xlabel('Tỷ Lệ Dương Tính Giả')
    plt.ylabel('Tỷ Lệ Dương Tính Thật')
    plt.title('Đường Cong ROC')
    plt.legend()

    # 4. Scatter plot của reconstruction errors
    plt.subplot(2, 3, 4)
    normal_indices = np.where(test_labels == 0)[0]
    pneumonia_indices = np.where(test_labels == 1)[0]

    plt.scatter(normal_indices, normal_errors, alpha=0.6, label='Bình thường', color='blue', s=10)
    plt.scatter(pneumonia_indices, pneumonia_errors, alpha=0.6, label='Viêm phổi', color='red', s=10)
    plt.axhline(threshold, color='black', linestyle='--', label=f'Ngưỡng: {threshold:.4f}')
    plt.xlabel('Chỉ Số Mẫu')
    plt.ylabel('Lỗi Tái Tạo')
    plt.title('Lỗi Tái Tạo Theo Mẫu')
    plt.legend()

    # 5. Tóm tắt metrics
    plt.subplot(2, 3, 5)
    accuracy = accuracy_score(test_labels, predictions)
    precision = precision_score(test_labels, predictions)
    recall = recall_score(test_labels, predictions)
    f1 = f1_score(test_labels, predictions)

    metrics_text = f"""
    Độ Chính Xác: {accuracy:.3f}
    Precision: {precision:.3f}
    Recall: {recall:.3f}
    F1-Score: {f1:.3f}
    ROC-AUC: {roc_auc_score(test_labels, reconstruction_errors):.3f}

    True Positives: {cm[1,1]}
    True Negatives: {cm[0,0]}
    False Positives: {cm[0,1]}
    False Negatives: {cm[1,0]}
    """

    plt.text(0.1, 0.5, metrics_text, fontsize=12, verticalalignment='center')
    plt.axis('off')
    plt.title('Chỉ Số Đánh Giá')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'evaluation_results.png'), dpi=300, bbox_inches='tight')
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='Đánh giá Autoencoder cho Phát hiện Bất thường Chụp X-quang Ngực')
    parser.add_argument('--data_dir', type=str, default='c:\\\\DeepLearning\\\\AE_Xray\\\\backend\\\\dataset',
                       help="Đường dẫn đến thư mục dataset")
    parser.add_argument('--model_path', type=str, default='./saved_model/autoencoder.h5',
                       help='Đường dẫn đến mô hình đã huấn luyện')
    parser.add_argument('--threshold_path', type=str, default='./saved_model/threshold.txt',
                       help='Đường dẫn đến file ngưỡng')
    parser.add_argument('--output_dir', type=str, default='./saved_model',
                       help='Thư mục lưu kết quả đánh giá')
    parser.add_argument('--img_size', type=int, default=128, help='Kích thước ảnh')

    args = parser.parse_args()

    # Tạo thư mục output nếu chưa tồn tại
    os.makedirs(args.output_dir, exist_ok=True)

    # Tải mô hình
    try:
        model = load_model(args.model_path, compile=False)
        print(f"Đã tải mô hình từ {args.model_path}")
    except Exception as e:
        print(f"Lỗi khi tải mô hình: {e}")
        return

    # Tải ngưỡng
    try:
        with open(args.threshold_path, 'r') as f:
            threshold = float(f.read().strip())
        print(f"Đã tải ngưỡng: {threshold}")
    except Exception as e:
        print(f"Lỗi khi tải ngưỡng: {e}")
        return

    # Tải dữ liệu test
    test_images, test_labels, test_paths = load_test_data(args.data_dir, args.img_size)
    if test_images is None:
        print("Không tìm thấy dữ liệu test. Thoát chương trình.")
        return

    # Đánh giá mô hình
    print("Đang đánh giá mô hình...")
    metrics, reconstruction_errors, predictions = evaluate_model(model, test_images, test_labels, threshold)

    # In kết quả
    print("\n" + "="*50)
    print("KẾT QUẢ ĐÁNH GIÁ")
    print("="*50)
    print(f"Độ chính xác: {metrics['accuracy']:.4f}")
    print(f"Precision: {metrics['precision']:.4f}")
    print(f"Recall: {metrics['recall']:.4f}")
    print(f"F1-Score: {metrics['f1_score']:.4f}")
    print(f"ROC-AUC: {metrics['roc_auc']:.4f}")
    print(f"Ngưỡng: {metrics['threshold']:.6f}")
    print(f"\nMa trận nhầm lẫn:")
    print(f"True Negatives: {metrics['true_negatives']}")
    print(f"False Positives: {metrics['false_positives']}")
    print(f"False Negatives: {metrics['false_negatives']}")
    print(f"True Positives: {metrics['true_positives']}")

    # Lưu metrics vào JSON
    metrics_path = os.path.join(args.output_dir, 'evaluation_metrics.json')
    with open(metrics_path, 'w') as f:
        json.dump(metrics, f, indent=2)
    print(f"\nĐã lưu metrics vào {metrics_path}")

    # Vẽ biểu đồ kết quả
    plot_evaluation_results(test_labels, reconstruction_errors, predictions, threshold, args.output_dir)
    print(f"Đã lưu biểu đồ đánh giá vào {os.path.join(args.output_dir, 'evaluation_results.png')}")

if __name__ == '__main__':
    main()
