#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script khôi phục toàn bộ dataset gốc để đạt hiệu suất 80%
"""

import os
import shutil
from pathlib import Path

def restore_full_dataset():
    """Khôi phục toàn bộ dataset gốc và tạo validation set hợp lý"""
    
    base_dir = Path("../../backend/dataset")
    backup_dir = base_dir / "backup_original"
    
    print("🔄 Khôi phục TOÀN BỘ dataset gốc để đạt 80% accuracy...")
    
    if not backup_dir.exists():
        print("❌ Không tìm thấy backup dataset gốc!")
        return False
    
    # Xóa dataset hiện tại
    train_normal = base_dir / "train" / "NORMAL"
    val_normal = base_dir / "val" / "NORMAL"
    
    if train_normal.exists():
        shutil.rmtree(train_normal)
    if val_normal.exists():
        shutil.rmtree(val_normal)
    
    # <PERSON>h<PERSON><PERSON> phụ<PERSON> từ backup
    backup_train = backup_dir / "train_NORMAL"
    backup_val = backup_dir / "val_NORMAL"
    
    # L<PERSON><PERSON> tất cả ảnh từ backup
    all_images = []
    if backup_train.exists():
        all_images.extend(list(backup_train.glob("*.jpeg")))
    if backup_val.exists():
        all_images.extend(list(backup_val.glob("*.jpeg")))
    
    print(f"📊 Tổng số ảnh NORMAL: {len(all_images)}")
    
    # Chia dataset: 90% train, 10% val (thay vì 500+50)
    import random
    random.seed(42)
    random.shuffle(all_images)
    
    split_idx = int(0.9 * len(all_images))
    train_images = all_images[:split_idx]
    val_images = all_images[split_idx:]
    
    print(f"📝 Chia dataset: {len(train_images)} train + {len(val_images)} val")
    
    # Tạo thư mục
    train_normal.mkdir(parents=True, exist_ok=True)
    val_normal.mkdir(parents=True, exist_ok=True)
    
    # Copy ảnh train
    print("📁 Tạo FULL train dataset...")
    for i, img_path in enumerate(train_images):
        new_name = f"train_normal_{i+1:04d}.jpeg"
        shutil.copy2(img_path, train_normal / new_name)
    
    # Copy ảnh val
    print("📁 Tạo FULL val dataset...")
    for i, img_path in enumerate(val_images):
        new_name = f"val_normal_{i+1:04d}.jpeg"
        shutil.copy2(img_path, val_normal / new_name)
    
    print("✅ Hoàn thành khôi phục FULL dataset!")
    print(f"   📊 Train: {len(list(train_normal.glob('*.jpeg')))} ảnh")
    print(f"   📊 Val: {len(list(val_normal.glob('*.jpeg')))} ảnh")
    print("   🎯 Dự kiến đạt 80%+ accuracy với dataset đầy đủ!")
    
    return True

if __name__ == "__main__":
    print("="*60)
    print("🚀 KHÔI PHỤC TOÀN BỘ DATASET CHO HIỆU SUẤT 80%")
    print("="*60)
    
    success = restore_full_dataset()
    
    if success:
        print("\n💡 KHUYẾN NGHỊ TRAINING MỚI:")
        print("1. Dataset: ~1,200+ train + ~130+ val")
        print("2. Epochs: 200 (với early stopping patience=25)")
        print("3. Batch size: 8 (giảm để fit memory)")
        print("4. Thời gian: 6-8 tiếng")
        print("5. Target: 80-85% accuracy")
        
        print("\n🗑️  Tự động xóa script này sau 3 giây...")
        import time
        time.sleep(3)
        
        try:
            os.remove(__file__)
            print("✅ Đã xóa script tạm thời!")
        except:
            print("⚠️  Không thể tự động xóa script.")
    else:
        print("\n❌ Khôi phục dataset thất bại!")
