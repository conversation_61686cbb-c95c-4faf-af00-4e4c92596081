import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Input, Conv2D, MaxPooling2D, UpSampling2D, BatchNormalization
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping

def build_autoencoder(input_shape=(128, 128, 1)):
    """
    <PERSON><PERSON><PERSON> dựng mô hình Autoencoder cho ph<PERSON>t hiện bất thường trong ảnh X-ray

    Args:
        input_shape: <PERSON><PERSON><PERSON> thước ảnh đầu vào (mặc định 128x128 grayscale)

    Returns:
        model: Keras Model đã biên dịch
    """
    # Input
    input_img = Input(shape=input_shape)

    # Encoder
    x = Conv2D(32, (3, 3), activation='relu', padding='same')(input_img)
    x = BatchNormalization()(x)
    x = MaxPooling2D((2, 2), padding='same')(x)

    x = Conv2D(64, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)
    x = MaxPooling2D((2, 2), padding='same')(x)

    x = Conv2D(128, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)
    encoded = MaxPooling2D((2, 2), padding='same')(x)

    # Decoder
    x = Conv2D(128, (3, 3), activation='relu', padding='same')(encoded)
    x = BatchNormalization()(x)
    x = UpSampling2D((2, 2))(x)

    x = Conv2D(64, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)
    x = UpSampling2D((2, 2))(x)

    x = Conv2D(32, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)
    x = UpSampling2D((2, 2))(x)

    # Output
    decoded = Conv2D(1, (3, 3), activation='sigmoid', padding='same')(x)

    # Autoencoder
    autoencoder = Model(input_img, decoded)
    autoencoder.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='mse',  # MSE tốt hơn cho reconstruction
        metrics=['mae']
    )

    return autoencoder

def get_callbacks(checkpoint_path):
    """
    Tạo callbacks cho quá trình huấn luyện

    Args:
        checkpoint_path: Đường dẫn lưu model checkpoint

    Returns:
        list: Danh sách các callbacks
    """
    checkpointer = ModelCheckpoint(
        filepath=checkpoint_path,
        monitor='val_loss',
        verbose=1,
        save_best_only=True
    )

    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=5,  # Giảm patience để training nhanh hơn
        restore_best_weights=True,
        verbose=1
    )

    return [checkpointer, early_stopping]