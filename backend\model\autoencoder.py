import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import (
    Input, Conv2D, MaxPooling2D, UpSampling2D, BatchNormalization,
    Dropout, Add, Concatenate, GlobalAveragePooling2D, Dense, Reshape,
    Conv2DTranspose, LeakyReLU, Activation
)
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers import AdamW
from tensorflow.keras.regularizers import l2
import tensorflow.keras.backend as K
import numpy as np

def ssim_loss(y_true, y_pred):
    """
    Tính toán SSIM loss để đánh giá chất lượng tái tạo ảnh

    Args:
        y_true: Ảnh gốc
        y_pred: Ảnh tái tạo

    Returns:
        SSIM loss (1 - SSIM)
    """
    return 1 - tf.reduce_mean(tf.image.ssim(y_true, y_pred, max_val=1.0))

def perceptual_loss(y_true, y_pred):
    """
    Tính toán perceptual loss sử dụng gradient magnitude

    Args:
        y_true: Ảnh gốc
        y_pred: Ảnh tái tạo

    Returns:
        Perceptual loss
    """
    # Tính gradient cho ảnh gốc và ảnh tái tạo
    def compute_gradient_magnitude(img):
        # Sobel filters
        sobel_x = tf.constant([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=tf.float32)
        sobel_y = tf.constant([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=tf.float32)

        sobel_x = tf.reshape(sobel_x, [3, 3, 1, 1])
        sobel_y = tf.reshape(sobel_y, [3, 3, 1, 1])

        grad_x = tf.nn.conv2d(img, sobel_x, strides=[1, 1, 1, 1], padding='SAME')
        grad_y = tf.nn.conv2d(img, sobel_y, strides=[1, 1, 1, 1], padding='SAME')

        return tf.sqrt(grad_x**2 + grad_y**2)

    grad_true = compute_gradient_magnitude(y_true)
    grad_pred = compute_gradient_magnitude(y_pred)

    return tf.reduce_mean(tf.abs(grad_true - grad_pred))

def combined_loss(y_true, y_pred):
    """
    Kết hợp nhiều loss functions để tối ưu chất lượng tái tạo

    Args:
        y_true: Ảnh gốc
        y_pred: Ảnh tái tạo

    Returns:
        Combined loss
    """
    # Trọng số cho từng loss component
    mse_weight = 0.4
    ssim_weight = 0.4
    perceptual_weight = 0.2

    # Tính các loss components
    mse_loss = tf.reduce_mean(tf.square(y_true - y_pred))
    ssim_loss_val = ssim_loss(y_true, y_pred)
    perceptual_loss_val = perceptual_loss(y_true, y_pred)

    # Kết hợp các loss
    total_loss = (mse_weight * mse_loss +
                  ssim_weight * ssim_loss_val +
                  perceptual_weight * perceptual_loss_val)

    return total_loss

def residual_block(x, filters, kernel_size=3, dropout_rate=0.3):
    """
    Tạo residual block với skip connection để cải thiện gradient flow

    Args:
        x: Input tensor
        filters: Số lượng filters
        kernel_size: Kích thước kernel
        dropout_rate: Tỷ lệ dropout

    Returns:
        Output tensor với residual connection
    """
    # Lưu input để tạo skip connection
    shortcut = x

    # First convolution
    x = Conv2D(filters, kernel_size, padding='same', kernel_regularizer=l2(1e-4))(x)
    x = BatchNormalization()(x)
    x = LeakyReLU(alpha=0.2)(x)
    x = Dropout(dropout_rate)(x)

    # Second convolution
    x = Conv2D(filters, kernel_size, padding='same', kernel_regularizer=l2(1e-4))(x)
    x = BatchNormalization()(x)

    # Điều chỉnh shortcut nếu cần thiết
    if shortcut.shape[-1] != filters:
        shortcut = Conv2D(filters, 1, padding='same')(shortcut)

    # Add skip connection
    x = Add()([x, shortcut])
    x = LeakyReLU(alpha=0.2)(x)

    return x

def attention_block(x, filters):
    """
    Tạo attention mechanism để tập trung vào các vùng quan trọng

    Args:
        x: Input tensor
        filters: Số lượng filters

    Returns:
        Output tensor với attention weights
    """
    # Global average pooling để tạo context vector
    gap = GlobalAveragePooling2D()(x)

    # Dense layers để học attention weights
    attention = Dense(filters // 8, activation='relu')(gap)
    attention = Dense(filters, activation='sigmoid')(attention)

    # Reshape để match với input dimensions
    attention = Reshape((1, 1, filters))(attention)

    # Apply attention weights
    x = x * attention

    return x

def build_autoencoder(input_shape=(256, 256, 1)):
    """
    Xây dựng mô hình Autoencoder nâng cao cho phát hiện bất thường trong ảnh X-ray

    Kiến trúc mới bao gồm:
    - Deeper encoder/decoder với residual connections
    - Attention mechanisms
    - Skip connections giữa encoder và decoder
    - Advanced loss function (SSIM + MSE + Perceptual)

    Args:
        input_shape: Kích thước ảnh đầu vào (mặc định 256x256 grayscale)

    Returns:
        model: Keras Model đã biên dịch với kiến trúc nâng cao
    """
    # Input
    input_img = Input(shape=input_shape)

    # ==================== ENCODER ====================
    # Block 1: 256x256 -> 128x128
    x1 = Conv2D(64, 3, padding='same', kernel_regularizer=l2(1e-4))(input_img)
    x1 = BatchNormalization()(x1)
    x1 = LeakyReLU(alpha=0.2)(x1)
    x1 = residual_block(x1, 64, dropout_rate=0.2)
    skip1 = x1  # Skip connection
    x1 = MaxPooling2D(2, padding='same')(x1)

    # Block 2: 128x128 -> 64x64
    x2 = residual_block(x1, 128, dropout_rate=0.25)
    x2 = residual_block(x2, 128, dropout_rate=0.25)
    x2 = attention_block(x2, 128)  # Attention mechanism
    skip2 = x2  # Skip connection
    x2 = MaxPooling2D(2, padding='same')(x2)

    # Block 3: 64x64 -> 32x32
    x3 = residual_block(x2, 256, dropout_rate=0.3)
    x3 = residual_block(x3, 256, dropout_rate=0.3)
    x3 = attention_block(x3, 256)
    skip3 = x3  # Skip connection
    x3 = MaxPooling2D(2, padding='same')(x3)

    # Block 4: 32x32 -> 16x16
    x4 = residual_block(x3, 512, dropout_rate=0.35)
    x4 = residual_block(x4, 512, dropout_rate=0.35)
    x4 = attention_block(x4, 512)
    skip4 = x4  # Skip connection
    x4 = MaxPooling2D(2, padding='same')(x4)

    # Bottleneck: 16x16
    encoded = residual_block(x4, 1024, dropout_rate=0.4)
    encoded = residual_block(encoded, 1024, dropout_rate=0.4)
    encoded = attention_block(encoded, 1024)

    # ==================== DECODER ====================
    # Block 1: 16x16 -> 32x32
    x = UpSampling2D(2)(encoded)
    x = Conv2D(512, 3, padding='same', kernel_regularizer=l2(1e-4))(x)
    x = BatchNormalization()(x)
    x = LeakyReLU(alpha=0.2)(x)
    x = Concatenate()([x, skip4])  # Skip connection
    x = residual_block(x, 512, dropout_rate=0.35)
    x = residual_block(x, 512, dropout_rate=0.35)

    # Block 2: 32x32 -> 64x64
    x = UpSampling2D(2)(x)
    x = Conv2D(256, 3, padding='same', kernel_regularizer=l2(1e-4))(x)
    x = BatchNormalization()(x)
    x = LeakyReLU(alpha=0.2)(x)
    x = Concatenate()([x, skip3])  # Skip connection
    x = residual_block(x, 256, dropout_rate=0.3)
    x = residual_block(x, 256, dropout_rate=0.3)

    # Block 3: 64x64 -> 128x128
    x = UpSampling2D(2)(x)
    x = Conv2D(128, 3, padding='same', kernel_regularizer=l2(1e-4))(x)
    x = BatchNormalization()(x)
    x = LeakyReLU(alpha=0.2)(x)
    x = Concatenate()([x, skip2])  # Skip connection
    x = residual_block(x, 128, dropout_rate=0.25)
    x = residual_block(x, 128, dropout_rate=0.25)

    # Block 4: 128x128 -> 256x256
    x = UpSampling2D(2)(x)
    x = Conv2D(64, 3, padding='same', kernel_regularizer=l2(1e-4))(x)
    x = BatchNormalization()(x)
    x = LeakyReLU(alpha=0.2)(x)
    x = Concatenate()([x, skip1])  # Skip connection
    x = residual_block(x, 64, dropout_rate=0.2)

    # Output layer - 256x256x1
    decoded = Conv2D(1, 3, activation='sigmoid', padding='same')(x)

    # ==================== MODEL COMPILATION ====================
    autoencoder = Model(input_img, decoded, name='Advanced_AutoEncoder')

    # Sử dụng AdamW optimizer với learning rate scheduling
    optimizer = AdamW(
        learning_rate=1e-3,
        weight_decay=1e-4,
        beta_1=0.9,
        beta_2=0.999
    )

    # Compile với combined loss function
    autoencoder.compile(
        optimizer=optimizer,
        loss=combined_loss,
        metrics=['mse', ssim_loss]
    )

    return autoencoder

def get_callbacks(checkpoint_path, patience=15):
    """
    Tạo callbacks nâng cao cho quá trình huấn luyện

    Args:
        checkpoint_path: Đường dẫn lưu model checkpoint
        patience: Số epochs chờ trước khi early stopping

    Returns:
        list: Danh sách các callbacks nâng cao
    """
    # Model checkpoint - lưu model tốt nhất
    checkpointer = ModelCheckpoint(
        filepath=checkpoint_path,
        monitor='val_loss',
        verbose=1,
        save_best_only=True,
        save_weights_only=False,
        mode='min'
    )

    # Early stopping với patience cao hơn
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=patience,
        restore_best_weights=True,
        verbose=1,
        mode='min'
    )

    # Learning rate reduction khi val_loss không cải thiện
    reduce_lr = ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=patience//3,
        min_lr=1e-7,
        verbose=1,
        mode='min'
    )

    return [checkpointer, early_stopping, reduce_lr]

def build_simple_autoencoder(input_shape=(128, 128, 1)):
    """
    Xây dựng mô hình Autoencoder đơn giản cho backward compatibility

    Args:
        input_shape: Kích thước ảnh đầu vào

    Returns:
        model: Keras Model đã biên dịch (kiến trúc cũ)
    """
    # Input
    input_img = Input(shape=input_shape)

    # Encoder
    x = Conv2D(32, (3, 3), activation='relu', padding='same')(input_img)
    x = BatchNormalization()(x)
    x = MaxPooling2D((2, 2), padding='same')(x)

    x = Conv2D(64, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)
    x = MaxPooling2D((2, 2), padding='same')(x)

    x = Conv2D(128, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)
    encoded = MaxPooling2D((2, 2), padding='same')(x)

    # Decoder
    x = Conv2D(128, (3, 3), activation='relu', padding='same')(encoded)
    x = BatchNormalization()(x)
    x = UpSampling2D((2, 2))(x)

    x = Conv2D(64, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)
    x = UpSampling2D((2, 2))(x)

    x = Conv2D(32, (3, 3), activation='relu', padding='same')(x)
    x = BatchNormalization()(x)
    x = UpSampling2D((2, 2))(x)

    # Output
    decoded = Conv2D(1, (3, 3), activation='sigmoid', padding='same')(x)

    # Autoencoder
    autoencoder = Model(input_img, decoded)
    autoencoder.compile(optimizer='adam', loss='binary_crossentropy')

    return autoencoder