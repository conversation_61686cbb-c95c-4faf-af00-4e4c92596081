@echo off
chcp 65001 > nul
echo ========================================
echo    HUAN LUYEN VA DANH GIA MO HINH (FAST MODE)
echo ========================================
echo.
echo [THONG TIN] Da toi uu cho training nhanh:
echo - Dataset: 200 anh train + 50 anh val (thay vi 1341+8)
echo - Epochs: 20 (thay vi 50)
echo - Batch size: 64 (thay vi 32)
echo - Model: AutoEncoder don gian
echo - Thoi gian du kien: 60-90 phut
echo.

cd backend\model

echo Dang huan luyen mo hinh voi du lieu NORMAL...
echo - Su dung train/NORMAL/ cho huan luyen
echo - Su dung val/NORMAL/ cho validation
echo.

set PYTHONIOENCODING=utf-8
python train.py --data_dir "..\..\backend\dataset" --img_size 128 --batch_size 64 --epochs 20 --output_dir "saved_model"

if %errorlevel% neq 0 (
    echo.
    echo [LOI] Huan luyen that bai!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Huan luyen hoan thanh! Bat dau danh gia...
echo ========================================
echo.

python evaluate.py --data_dir "..\..\backend\dataset" --model_path "saved_model\autoencoder.h5" --threshold_path "saved_model\threshold.txt" --output_dir "saved_model"

if %errorlevel% neq 0 (
    echo.
    echo [LOI] Danh gia that bai!
    pause
    exit /b 1
)

echo.
echo ========================================
echo        HOAN THANH TOAN BO QUY TRINH
echo ========================================
echo.
echo KET QUA HUAN LUYEN:
echo - Mo hinh: backend\model\saved_model\autoencoder.h5
echo - Nguong phat hien: backend\model\saved_model\threshold.txt
echo - Bieu do training: backend\model\training_history.png
echo.
echo KET QUA DANH GIA:
echo - Metrics: backend\model\saved_model\evaluation_metrics.json
echo - Bieu do evaluation: backend\model\saved_model\evaluation_results.png
echo.
echo Ban co the chay ung dung bang cach chay: run_app.bat
echo.

pause
