#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script cải thiện dataset để tăng hiệu suất model
"""

import os
import shutil
import random
from pathlib import Path

def improve_dataset():
    """Tăng dataset từ 200 lên 500 ảnh train"""
    
    # Đường dẫn dataset
    base_dir = Path("../../backend/dataset")
    train_normal = base_dir / "train" / "NORMAL"
    val_normal = base_dir / "val" / "NORMAL"
    backup_dir = base_dir / "backup_original"
    
    print("🔄 Cải thiện dataset để tăng hiệu suất...")
    
    # Kiểm tra backup
    if not backup_dir.exists():
        print("❌ Không tìm thấy backup dataset gốc!")
        print("💡 Hãy chạy lại script tạo dataset để có backup.")
        return False
    
    # Lấy danh sách tất cả ảnh NORMAL từ backup
    all_normal_images = []
    backup_train = backup_dir / "train_NORMAL"
    backup_val = backup_dir / "val_NORMAL"
    
    if backup_train.exists():
        all_normal_images.extend(list(backup_train.glob("*.jpeg")))
    if backup_val.exists():
        all_normal_images.extend(list(backup_val.glob("*.jpeg")))
    
    print(f"📊 Tổng số ảnh NORMAL có sẵn: {len(all_normal_images)}")
    
    # Shuffle và chọn subset lớn hơn
    random.seed(42)  # Để reproducible
    random.shuffle(all_normal_images)
    
    # Chọn 550 ảnh: 500 train + 50 val
    selected_train = all_normal_images[:500]
    selected_val = all_normal_images[500:550]
    
    print(f"📝 Tăng lên {len(selected_train)} ảnh train, {len(selected_val)} ảnh val")
    
    # Xóa dataset cũ
    if train_normal.exists():
        shutil.rmtree(train_normal)
    if val_normal.exists():
        shutil.rmtree(val_normal)
    
    # Tạo thư mục mới
    train_normal.mkdir(parents=True, exist_ok=True)
    val_normal.mkdir(parents=True, exist_ok=True)
    
    # Copy ảnh train
    print("📁 Tạo train dataset cải thiện...")
    for i, img_path in enumerate(selected_train):
        new_name = f"train_normal_{i+1:03d}.jpeg"
        shutil.copy2(img_path, train_normal / new_name)
    
    # Copy ảnh val
    print("📁 Tạo val dataset cải thiện...")
    for i, img_path in enumerate(selected_val):
        new_name = f"val_normal_{i+1:03d}.jpeg"
        shutil.copy2(img_path, val_normal / new_name)
    
    print("✅ Hoàn thành cải thiện dataset!")
    print(f"   📊 Train: {len(list(train_normal.glob('*.jpeg')))} ảnh")
    print(f"   📊 Val: {len(list(val_normal.glob('*.jpeg')))} ảnh")
    print("   🎯 Dự kiến cải thiện hiệu suất đáng kể!")
    
    return True

if __name__ == "__main__":
    print("="*50)
    print("🚀 CẢI THIỆN DATASET CHO HIỆU SUẤT CAO HỚN")
    print("="*50)
    
    success = improve_dataset()
    
    if success:
        print("\n💡 KHUYẾN NGHỊ TIẾP THEO:")
        print("1. Chạy training với epochs cao hơn:")
        print("   python train.py --epochs 50 --batch_size 32")
        print("2. Thời gian training dự kiến: 2-3 tiếng")
        print("3. Hiệu suất dự kiến: 70-80% accuracy")
        
        print("\n🗑️  Tự động xóa script này sau 3 giây...")
        import time
        time.sleep(3)
        
        try:
            os.remove(__file__)
            print("✅ Đã xóa script tạm thời!")
        except:
            print("⚠️  Không thể tự động xóa script.")
    else:
        print("\n❌ Cải thiện dataset thất bại!")
