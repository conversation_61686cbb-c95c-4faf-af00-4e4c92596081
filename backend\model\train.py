import os
import numpy as np
import matplotlib.pyplot as plt
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import tensorflow as tf
import cv2
from skimage import exposure
from sklearn.model_selection import train_test_split
import json
import time
from datetime import datetime

import glob
from PIL import Image, ImageEnhance
import argparse
from autoencoder import build_autoencoder, get_callbacks

def apply_clahe(image, clip_limit=2.0, tile_grid_size=(8, 8)):
    """
    Áp dụng CLAHE (Contrast Limited Adaptive Histogram Equalization) để cải thiện contrast

    Args:
        image: Ảnh đầu vào (numpy array)
        clip_limit: Giới hạn contrast
        tile_grid_size: Kích thước grid cho adaptive histogram

    Returns:
        Ảnh đã được cải thiện contrast
    """
    # Chuyển về uint8 nếu cần
    if image.dtype != np.uint8:
        image_uint8 = (image * 255).astype(np.uint8)
    else:
        image_uint8 = image

    # Áp dụng CLAHE
    clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
    enhanced = clahe.apply(image_uint8)

    # Chuyển về float32 và normalize
    return enhanced.astype(np.float32) / 255.0

def apply_gaussian_noise_reduction(image, kernel_size=3):
    """
    Áp dụng Gaussian blur để giảm noise

    Args:
        image: Ảnh đầu vào
        kernel_size: Kích thước kernel cho Gaussian blur

    Returns:
        Ảnh đã giảm noise
    """
    if image.dtype != np.uint8:
        image_uint8 = (image * 255).astype(np.uint8)
    else:
        image_uint8 = image

    # Áp dụng Gaussian blur
    denoised = cv2.GaussianBlur(image_uint8, (kernel_size, kernel_size), 0)

    return denoised.astype(np.float32) / 255.0

def enhance_edges(image, alpha=1.5):
    """
    Tăng cường edges để làm nổi bật các đặc trưng quan trọng

    Args:
        image: Ảnh đầu vào
        alpha: Hệ số tăng cường edges

    Returns:
        Ảnh đã tăng cường edges
    """
    if image.dtype != np.uint8:
        image_uint8 = (image * 255).astype(np.uint8)
    else:
        image_uint8 = image

    # Tạo kernel để detect edges
    kernel = np.array([[-1, -1, -1],
                       [-1,  9, -1],
                       [-1, -1, -1]])

    # Áp dụng filter
    enhanced = cv2.filter2D(image_uint8, -1, kernel)

    # Blend với ảnh gốc
    result = cv2.addWeighted(image_uint8, 1.0, enhanced, alpha-1.0, 0)

    return np.clip(result, 0, 255).astype(np.float32) / 255.0

def standardize_intensity(image):
    """
    Chuẩn hóa intensity distribution của ảnh

    Args:
        image: Ảnh đầu vào

    Returns:
        Ảnh đã được chuẩn hóa intensity
    """
    # Tính mean và std
    mean = np.mean(image)
    std = np.std(image)

    # Standardization: (x - mean) / std
    if std > 0:
        standardized = (image - mean) / std
        # Rescale về [0, 1]
        standardized = (standardized - standardized.min()) / (standardized.max() - standardized.min())
    else:
        standardized = image

    return standardized.astype(np.float32)

def advanced_preprocessing_pipeline(image, img_size=256):
    """
    Pipeline tiền xử lý nâng cao cho ảnh X-ray

    Args:
        image: Ảnh PIL hoặc numpy array
        img_size: Kích thước ảnh đầu ra

    Returns:
        Ảnh đã được tiền xử lý
    """
    # Chuyển về numpy array nếu là PIL Image
    if hasattr(image, 'convert'):
        image = image.convert('L')  # Chuyển sang grayscale
        image = image.resize((img_size, img_size), Image.Resampling.LANCZOS)
        img_array = np.array(image)
    else:
        img_array = image

    # 1. Áp dụng CLAHE để cải thiện contrast
    img_array = apply_clahe(img_array, clip_limit=3.0, tile_grid_size=(8, 8))

    # 2. Giảm noise
    img_array = apply_gaussian_noise_reduction(img_array, kernel_size=3)

    # 3. Tăng cường edges (nhẹ để không làm mất thông tin)
    img_array = enhance_edges(img_array, alpha=1.2)

    # 4. Chuẩn hóa intensity
    img_array = standardize_intensity(img_array)

    return img_array

def load_and_preprocess_data(data_dir, img_size=256, batch_size=32, use_advanced_preprocessing=True):
    """
    Tải và tiền xử lý dữ liệu nâng cao từ thư mục - CHỈ SỬ DỤNG ẢNH NORMAL

    Args:
        data_dir: Đường dẫn đến thư mục chứa thư mục 'train' và 'val'
        img_size: Kích thước ảnh đầu vào (mặc định 256x256)
        batch_size: Kích thước batch
        use_advanced_preprocessing: Sử dụng preprocessing nâng cao (CLAHE, noise reduction, etc.)

    Returns:
        train_generator, val_generator, val_images: Data generators và validation images
    """
    print(f"🔄 Bắt đầu tải dữ liệu với kích thước ảnh: {img_size}x{img_size}")
    print(f"📊 Preprocessing nâng cao: {'Bật' if use_advanced_preprocessing else 'Tắt'}")

    # Lấy ảnh NORMAL từ thư mục train/NORMAL/
    train_normal_pattern = os.path.join(data_dir, 'train', 'NORMAL', '*.jpeg')
    train_normal_images = glob.glob(train_normal_pattern)
    print(f"📁 Tìm thấy {len(train_normal_images)} ảnh NORMAL cho huấn luyện")

    # Lấy ảnh NORMAL từ thư mục val/NORMAL/
    val_normal_pattern = os.path.join(data_dir, 'val', 'NORMAL', '*.jpeg')
    val_normal_images = glob.glob(val_normal_pattern)
    print(f"📁 Tìm thấy {len(val_normal_images)} ảnh NORMAL cho validation")

    if not train_normal_images:
        print(f"❌ Cảnh báo: Không tìm thấy ảnh NORMAL cho huấn luyện tại {train_normal_pattern}")
        return None, None, None

    if not val_normal_images:
        print(f"❌ Cảnh báo: Không tìm thấy ảnh NORMAL cho validation tại {val_normal_pattern}")
        return None, None, None

    # Tải và tiền xử lý ảnh training (NORMAL)
    print("🔄 Đang xử lý ảnh training...")
    train_images = []
    for i, img_path in enumerate(train_normal_images):
        try:
            img = Image.open(img_path)

            if use_advanced_preprocessing:
                # Sử dụng pipeline preprocessing nâng cao
                img_array = advanced_preprocessing_pipeline(img, img_size)
            else:
                # Preprocessing đơn giản (backward compatibility)
                img = img.convert('L')
                img = img.resize((img_size, img_size))
                img_array = np.array(img) / 255.0

            train_images.append(img_array)

            # Progress indicator
            if (i + 1) % 100 == 0:
                print(f"   Đã xử lý {i + 1}/{len(train_normal_images)} ảnh training")

        except Exception as e:
            print(f"⚠️ Lỗi khi xử lý {img_path}: {e}")

    # Tải và tiền xử lý ảnh validation (NORMAL)
    print("🔄 Đang xử lý ảnh validation...")
    val_images = []
    for i, img_path in enumerate(val_normal_images):
        try:
            img = Image.open(img_path)

            if use_advanced_preprocessing:
                # Sử dụng pipeline preprocessing nâng cao
                img_array = advanced_preprocessing_pipeline(img, img_size)
            else:
                # Preprocessing đơn giản
                img = img.convert('L')
                img = img.resize((img_size, img_size))
                img_array = np.array(img) / 255.0

            val_images.append(img_array)

        except Exception as e:
            print(f"⚠️ Lỗi khi xử lý {img_path}: {e}")

    # Chuyển về numpy arrays
    train_images = np.array(train_images)
    val_images = np.array(val_images)

    # Thêm chiều kênh nếu cần
    if len(train_images.shape) == 3:
        train_images = np.expand_dims(train_images, axis=-1)
    if len(val_images.shape) == 3:
        val_images = np.expand_dims(val_images, axis=-1)

    print(f"✅ Hoàn thành preprocessing:")
    print(f"   - Training images: {train_images.shape}")
    print(f"   - Validation images: {val_images.shape}")
    print(f"   - Pixel value range: [{train_images.min():.3f}, {train_images.max():.3f}]")

    # Data augmentation nâng cao cho medical imaging
    train_datagen = ImageDataGenerator(
        rotation_range=15,          # Tăng từ 10 độ
        width_shift_range=0.15,     # Tăng từ 0.1
        height_shift_range=0.15,    # Tăng từ 0.1
        zoom_range=0.15,            # Tăng từ 0.1
        horizontal_flip=True,
        brightness_range=[0.8, 1.2], # Thêm brightness variation
        fill_mode='nearest',
        # Thêm các augmentation phù hợp với medical imaging
        shear_range=0.1,            # Shear transformation
        channel_shift_range=0.1,    # Channel shift
    )

    val_datagen = ImageDataGenerator()  # Không augment validation data

    # Tạo generators
    train_generator = train_datagen.flow(
        train_images, train_images,
        batch_size=batch_size,
        shuffle=True
    )

    val_generator = val_datagen.flow(
        val_images, val_images,
        batch_size=batch_size,
        shuffle=False
    )

    print(f"🎯 Data generators đã sẵn sàng:")
    print(f"   - Training batches: {len(train_generator)}")
    print(f"   - Validation batches: {len(val_generator)}")

    return train_generator, val_generator, val_images

def calculate_reconstruction_threshold(model, val_images, percentiles=[90, 92, 95, 97, 99]):
    """
    Tính toán ngưỡng phát hiện bất thường với multiple percentiles để tối ưu

    Args:
        model: Mô hình autoencoder đã train
        val_images: Tập validation
        percentiles: Danh sách các percentiles để test

    Returns:
        threshold_info: Dictionary chứa thông tin về các ngưỡng
    """
    print("🔄 Đang tính toán ngưỡng phát hiện bất thường...")

    # Dự đoán trên tập validation
    reconstructions = model.predict(val_images, verbose=0)

    # Tính MSE cho từng ảnh
    mse = np.mean(np.square(val_images - reconstructions), axis=(1, 2, 3))

    # Tính thống kê cơ bản
    mean_mse = np.mean(mse)
    std_mse = np.std(mse)
    min_mse = np.min(mse)
    max_mse = np.max(mse)

    print(f"📊 Thống kê lỗi tái tạo trên validation set:")
    print(f"   - Mean MSE: {mean_mse:.6f}")
    print(f"   - Std MSE: {std_mse:.6f}")
    print(f"   - Min MSE: {min_mse:.6f}")
    print(f"   - Max MSE: {max_mse:.6f}")

    # Tính ngưỡng cho các percentiles khác nhau
    threshold_info = {
        'mse_statistics': {
            'mean': float(mean_mse),
            'std': float(std_mse),
            'min': float(min_mse),
            'max': float(max_mse)
        },
        'thresholds': {}
    }

    print(f"🎯 Ngưỡng cho các percentiles:")
    for percentile in percentiles:
        threshold = np.percentile(mse, percentile)
        threshold_info['thresholds'][f'p{percentile}'] = float(threshold)
        print(f"   - {percentile}th percentile: {threshold:.6f}")

    # Sử dụng 95th percentile làm default
    default_threshold = threshold_info['thresholds']['p95']
    threshold_info['default_threshold'] = default_threshold

    print(f"✅ Ngưỡng mặc định (95th percentile): {default_threshold:.6f}")

    return threshold_info

def visualize_results(model, val_images, output_dir='./saved_model', num_samples=8):
    """
    Hiển thị kết quả tái tạo của autoencoder với visualization nâng cao

    Args:
        model: Mô hình autoencoder đã train
        val_images: Tập validation
        output_dir: Thư mục lưu kết quả
        num_samples: Số lượng mẫu hiển thị
    """
    print("🎨 Đang tạo visualization kết quả...")

    # Lấy một số mẫu từ tập validation
    indices = np.random.randint(0, len(val_images), num_samples)
    samples = val_images[indices]

    # Dự đoán (tái tạo)
    reconstructions = model.predict(samples, verbose=0)

    # Tính reconstruction errors
    mse_errors = np.mean(np.square(samples - reconstructions), axis=(1, 2, 3))

    # Hiển thị kết quả với layout cải tiến
    fig, axes = plt.subplots(4, num_samples, figsize=(20, 12))

    for i in range(num_samples):
        # Ảnh gốc
        axes[0, i].imshow(samples[i].squeeze(), cmap='gray')
        axes[0, i].set_title(f'Ảnh gốc #{i+1}', fontsize=10)
        axes[0, i].axis('off')

        # Ảnh tái tạo
        axes[1, i].imshow(reconstructions[i].squeeze(), cmap='gray')
        axes[1, i].set_title(f'Ảnh tái tạo\nMSE: {mse_errors[i]:.4f}', fontsize=10)
        axes[1, i].axis('off')

        # Sự khác biệt (absolute difference)
        diff = np.abs(samples[i] - reconstructions[i]).squeeze()
        im1 = axes[2, i].imshow(diff, cmap='hot')
        axes[2, i].set_title('Absolute Diff', fontsize=10)
        axes[2, i].axis('off')
        plt.colorbar(im1, ax=axes[2, i], fraction=0.046, pad=0.04)

        # Heatmap của reconstruction error
        squared_diff = np.square(samples[i] - reconstructions[i]).squeeze()
        im2 = axes[3, i].imshow(squared_diff, cmap='jet')
        axes[3, i].set_title('Squared Error', fontsize=10)
        axes[3, i].axis('off')
        plt.colorbar(im2, ax=axes[3, i], fraction=0.046, pad=0.04)

    plt.suptitle('Kết quả Tái tạo AutoEncoder', fontsize=16, y=0.98)
    plt.tight_layout()

    # Lưu kết quả vào thư mục saved_model
    output_path = os.path.join(output_dir, 'reconstruction_results.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"✅ Đã lưu visualization tại: {output_path}")

def save_training_artifacts(history, threshold_info, model_summary, output_dir):
    """
    Lưu các artifacts từ quá trình training

    Args:
        history: Training history
        threshold_info: Thông tin ngưỡng
        model_summary: Tóm tắt mô hình
        output_dir: Thư mục đầu ra
    """
    print("💾 Đang lưu training artifacts...")

    # Lưu threshold info
    threshold_path = os.path.join(output_dir, 'threshold_info.json')
    with open(threshold_path, 'w') as f:
        json.dump(threshold_info, f, indent=2)

    # Lưu training history
    history_path = os.path.join(output_dir, 'training_history.json')
    history_dict = {
        'loss': history.history['loss'],
        'val_loss': history.history['val_loss'],
        'mse': history.history.get('mse', []),
        'val_mse': history.history.get('val_mse', []),
        'ssim_loss': history.history.get('ssim_loss', []),
        'val_ssim_loss': history.history.get('val_ssim_loss', [])
    }
    with open(history_path, 'w') as f:
        json.dump(history_dict, f, indent=2)

    # Lưu model summary
    summary_path = os.path.join(output_dir, 'model_summary.txt')
    with open(summary_path, 'w') as f:
        f.write(model_summary)

    print(f"✅ Đã lưu artifacts:")
    print(f"   - Threshold info: {threshold_path}")
    print(f"   - Training history: {history_path}")
    print(f"   - Model summary: {summary_path}")

def plot_enhanced_training_history(history, output_dir):
    """
    Vẽ biểu đồ training history nâng cao

    Args:
        history: Training history object
        output_dir: Thư mục lưu biểu đồ
    """
    print("📊 Đang tạo biểu đồ training history...")

    # Tạo figure với multiple subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # Loss plot
    axes[0, 0].plot(history.history['loss'], label='Training Loss', linewidth=2)
    axes[0, 0].plot(history.history['val_loss'], label='Validation Loss', linewidth=2)
    axes[0, 0].set_title('Model Loss', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # MSE plot (nếu có)
    if 'mse' in history.history:
        axes[0, 1].plot(history.history['mse'], label='Training MSE', linewidth=2)
        axes[0, 1].plot(history.history['val_mse'], label='Validation MSE', linewidth=2)
        axes[0, 1].set_title('Mean Squared Error', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('MSE')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

    # SSIM Loss plot (nếu có)
    if 'ssim_loss' in history.history:
        axes[1, 0].plot(history.history['ssim_loss'], label='Training SSIM Loss', linewidth=2)
        axes[1, 0].plot(history.history['val_ssim_loss'], label='Validation SSIM Loss', linewidth=2)
        axes[1, 0].set_title('SSIM Loss', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('SSIM Loss')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

    # Learning rate plot (nếu có)
    if hasattr(history, 'lr') or 'lr' in history.history:
        lr_values = history.history.get('lr', [])
        if lr_values:
            axes[1, 1].plot(lr_values, label='Learning Rate', linewidth=2, color='red')
            axes[1, 1].set_title('Learning Rate Schedule', fontsize=14, fontweight='bold')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Learning Rate')
            axes[1, 1].set_yscale('log')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
    else:
        # Nếu không có LR, hiển thị loss comparison
        epochs = range(1, len(history.history['loss']) + 1)
        axes[1, 1].plot(epochs, history.history['loss'], 'b-', label='Training Loss')
        axes[1, 1].plot(epochs, history.history['val_loss'], 'r-', label='Validation Loss')
        axes[1, 1].set_title('Loss Comparison', fontsize=14, fontweight='bold')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Loss')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

    plt.suptitle('Training History - Advanced AutoEncoder', fontsize=16, fontweight='bold')
    plt.tight_layout()

    # Lưu biểu đồ vào thư mục saved_model
    output_path = os.path.join(output_dir, 'training_history.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"✅ Đã lưu training history tại: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='Huấn luyện AutoEncoder nâng cao để phát hiện bất thường trong ảnh X-ray')
    parser.add_argument('--data_dir', type=str, default='c:\\\\DeepLearning\\\\AE_Xray\\\\backend\\\\dataset',
                       help="Đường dẫn đến thư mục dataset")
    parser.add_argument('--img_size', type=int, default=256,
                       help='Kích thước ảnh (mặc định 256 cho kiến trúc mới)')
    parser.add_argument('--batch_size', type=int, default=16,
                       help='Kích thước batch (giảm xuống do model lớn hơn)')
    parser.add_argument('--epochs', type=int, default=100,
                       help='Số epoch (tăng lên cho training tốt hơn)')
    parser.add_argument('--output_dir', type=str, default='./saved_model',
                       help='Thư mục lưu mô hình')
    parser.add_argument('--use_advanced_preprocessing', action='store_true', default=True,
                       help='Sử dụng preprocessing nâng cao (CLAHE, noise reduction)')
    parser.add_argument('--patience', type=int, default=15,
                       help='Patience cho early stopping')

    args = parser.parse_args()

    # In thông tin cấu hình
    print("="*60)
    print("🚀 TRAINING AUTOENCODER NÂNG CAO CHO PHÁT HIỆN BẤT THƯỜNG X-RAY")
    print("="*60)
    print(f"📁 Dataset: {args.data_dir}")
    print(f"🖼️  Image size: {args.img_size}x{args.img_size}")
    print(f"📦 Batch size: {args.batch_size}")
    print(f"🔄 Epochs: {args.epochs}")
    print(f"🧠 Advanced preprocessing: {'Bật' if args.use_advanced_preprocessing else 'Tắt'}")
    print(f"⏰ Early stopping patience: {args.patience}")
    print(f"💾 Output directory: {args.output_dir}")
    print("="*60)

    # Ghi lại thời gian bắt đầu
    start_time = time.time()
    training_start = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"🕐 Bắt đầu training: {training_start}")

    # Tạo thư mục đầu ra nếu chưa tồn tại
    os.makedirs(args.output_dir, exist_ok=True)

    # Tải và tiền xử lý dữ liệu
    print("\n" + "="*40)
    print("📊 BƯỚC 1: TẢI VÀ TIỀN XỬ LÝ DỮ LIỆU")
    print("="*40)

    train_generator, val_generator, val_images = load_and_preprocess_data(
        args.data_dir,
        img_size=args.img_size,
        batch_size=args.batch_size,
        use_advanced_preprocessing=args.use_advanced_preprocessing
    )

    if train_generator is None:
        print("❌ Thoát do không tìm thấy dữ liệu huấn luyện.")
        return

    # Xây dựng mô hình
    print("\n" + "="*40)
    print("🏗️  BƯỚC 2: XÂY DỰNG MÔ HÌNH")
    print("="*40)

    autoencoder = build_autoencoder(input_shape=(args.img_size, args.img_size, 1))

    # Lấy model summary
    from io import StringIO
    summary_buffer = StringIO()
    autoencoder.summary(print_fn=lambda x: summary_buffer.write(x + '\n'))
    model_summary = summary_buffer.getvalue()

    print(f"✅ Mô hình đã được xây dựng:")
    print(f"   - Tổng số parameters: {autoencoder.count_params():,}")
    print(f"   - Input shape: {autoencoder.input_shape}")
    print(f"   - Output shape: {autoencoder.output_shape}")

    # Lấy callbacks nâng cao
    callbacks = get_callbacks(
        os.path.join(args.output_dir, 'autoencoder.h5'),
        patience=args.patience
    )

    # Huấn luyện mô hình
    print("\n" + "="*40)
    print("🎯 BƯỚC 3: HUẤN LUYỆN MÔ HÌNH")
    print("="*40)
    print("🔄 Bắt đầu quá trình huấn luyện...")

    history = autoencoder.fit(
        train_generator,
        steps_per_epoch=len(train_generator),
        epochs=args.epochs,
        validation_data=val_generator,
        validation_steps=len(val_generator),
        callbacks=callbacks,
        verbose=1
    )

    # Tính thời gian training
    training_time = time.time() - start_time
    training_end = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    print(f"\n✅ Hoàn thành training:")
    print(f"   - Thời gian training: {training_time/3600:.2f} giờ")
    print(f"   - Kết thúc lúc: {training_end}")
    print(f"   - Epochs đã train: {len(history.history['loss'])}")

    # Tải mô hình tốt nhất
    print("\n" + "="*40)
    print("📈 BƯỚC 4: ĐÁNH GIÁ VÀ LƯU KẾT QUẢ")
    print("="*40)

    autoencoder.load_weights(os.path.join(args.output_dir, 'autoencoder.h5'))
    print("✅ Đã tải mô hình tốt nhất")

    # Tính toán ngưỡng nâng cao
    threshold_info = calculate_reconstruction_threshold(autoencoder, val_images)

    # Lưu ngưỡng (backward compatibility)
    with open(os.path.join(args.output_dir, 'threshold.txt'), 'w') as f:
        f.write(str(threshold_info['default_threshold']))

    # Tạo visualizations
    visualize_results(autoencoder, val_images, args.output_dir)
    plot_enhanced_training_history(history, args.output_dir)

    # Lưu training artifacts
    save_training_artifacts(history, threshold_info, model_summary, args.output_dir)

    # Tóm tắt kết quả
    print("\n" + "="*60)
    print("🎉 HOÀN THÀNH TRAINING AUTOENCODER NÂNG CAO")
    print("="*60)
    print(f"📁 Các file đã được lưu tại: {args.output_dir}")
    print("📋 Danh sách files:")
    print(f"   ✅ autoencoder.h5 - Mô hình đã train")
    print(f"   ✅ threshold.txt - Ngưỡng phát hiện (backward compatibility)")
    print(f"   ✅ threshold_info.json - Thông tin ngưỡng chi tiết")
    print(f"   ✅ training_history.json - Lịch sử training")
    print(f"   ✅ training_history.png - Biểu đồ training")
    print(f"   ✅ reconstruction_results.png - Kết quả tái tạo")
    print(f"   ✅ model_summary.txt - Tóm tắt mô hình")

    print(f"\n🎯 Ngưỡng phát hiện bất thường: {threshold_info['default_threshold']:.6f}")
    print(f"⏱️  Tổng thời gian training: {training_time/3600:.2f} giờ")
    print("\n🚀 Sẵn sàng để chạy evaluation và sử dụng mô hình!")
    print("="*60)

if __name__ == '__main__':
    main()
