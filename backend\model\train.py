import os
import numpy as np
import matplotlib.pyplot as plt
from tensorflow.keras.preprocessing.image import ImageDataGenerator

import glob
from PIL import Image
import argparse
from autoencoder import build_autoencoder, get_callbacks

def load_and_preprocess_data(data_dir, img_size=128, batch_size=32):
    """
    Tải và tiền xử lý dữ liệu từ thư mục - CHỈ SỬ DỤNG ẢNH NORMAL

    Args:
        data_dir: Đường dẫn đến thư mục chứa thư mục 'train' và 'val' (ví dụ: 'C:\\DeepLearning\\AE_Xray\\backend\\dataset')
        img_size: Kích thước ảnh đầu vào
        batch_size: Kích thước batch

    Returns:
        train_generator, val_generator, val_images: Data generators cho train và validation, và val_images để tính threshold
    """
    # Lấy ảnh NORMAL từ thư mục train/NORMAL/
    train_normal_pattern = os.path.join(data_dir, 'train', 'NORMAL', '*.jpeg')
    train_normal_images = glob.glob(train_normal_pattern)
    print(f"Tìm thấy {len(train_normal_images)} ảnh NORMAL cho huấn luyện")

    # Lấy ảnh NORMAL từ thư mục val/NORMAL/
    val_normal_pattern = os.path.join(data_dir, 'val', 'NORMAL', '*.jpeg')
    val_normal_images = glob.glob(val_normal_pattern)
    print(f"Tìm thấy {len(val_normal_images)} ảnh NORMAL cho validation")

    if not train_normal_images:
        print(f"Cảnh báo: Không tìm thấy ảnh NORMAL cho huấn luyện tại {train_normal_pattern}")
        return None, None, None

    if not val_normal_images:
        print(f"Cảnh báo: Không tìm thấy ảnh NORMAL cho validation tại {val_normal_pattern}")
        return None, None, None

    # Tải và tiền xử lý ảnh training (NORMAL)
    train_images = []
    for img_path in train_normal_images:
        img = Image.open(img_path).convert('L')  # Chuyển sang ảnh xám
        img = img.resize((img_size, img_size))
        img_array = np.array(img) / 255.0
        train_images.append(img_array)

    # Tải và tiền xử lý ảnh validation (NORMAL)
    val_images = []
    for img_path in val_normal_images:
        img = Image.open(img_path).convert('L')  # Chuyển sang ảnh xám
        img = img.resize((img_size, img_size))
        img_array = np.array(img) / 255.0
        val_images.append(img_array)

    train_images = np.array(train_images)
    val_images = np.array(val_images)

    train_images = np.expand_dims(train_images, axis=-1)  # Thêm chiều kênh
    val_images = np.expand_dims(val_images, axis=-1)  # Thêm chiều kênh

    # Tăng cường dữ liệu
    train_datagen = ImageDataGenerator(
        rotation_range=10,
        width_shift_range=0.1,
        height_shift_range=0.1,
        zoom_range=0.1,
        horizontal_flip=True,
    )

    val_datagen = ImageDataGenerator()

    # Tạo generators
    train_generator = train_datagen.flow(
        train_images, train_images,
        batch_size=batch_size
    )

    val_generator = val_datagen.flow(
        val_images, val_images,
        batch_size=batch_size
    )

    return train_generator, val_generator, val_images

def calculate_reconstruction_threshold(model, val_images, percentile=90):
    """
    Tính toán ngưỡng phát hiện bất thường nâng cao với nhiều metrics

    Args:
        model: Mô hình autoencoder đã train
        val_images: Tập validation (chỉ NORMAL)
        percentile: Phần trăm ngưỡng (giảm xuống 90% để sensitive hơn)

    Returns:
        threshold: Giá trị ngưỡng tối ưu
    """
    print("🔄 Tính toán ngưỡng phát hiện bất thường nâng cao...")

    # Dự đoán trên tập validation
    reconstructions = model.predict(val_images, verbose=0)

    # Tính nhiều loại reconstruction errors
    mse = np.mean(np.square(val_images - reconstructions), axis=(1, 2, 3))
    mae = np.mean(np.abs(val_images - reconstructions), axis=(1, 2, 3))

    # Sử dụng binary cross-entropy error (phù hợp với loss function)
    bce = -np.mean(val_images * np.log(reconstructions + 1e-8) +
                   (1 - val_images) * np.log(1 - reconstructions + 1e-8), axis=(1, 2, 3))

    # Xác định ngưỡng dựa trên BCE (chính là loss function)
    threshold = np.percentile(bce, percentile)

    print(f"📊 Thống kê reconstruction errors:")
    print(f"   - MSE: min={mse.min():.6f}, max={mse.max():.6f}, mean={mse.mean():.6f}")
    print(f"   - MAE: min={mae.min():.6f}, max={mae.max():.6f}, mean={mae.mean():.6f}")
    print(f"   - BCE: min={bce.min():.6f}, max={bce.max():.6f}, mean={bce.mean():.6f}")
    print(f"   - Threshold (BCE {percentile}%): {threshold:.6f}")

    return threshold

def visualize_results(model, val_images, output_dir, num_samples=5):
    """
    Hiển thị kết quả tái tạo của autoencoder

    Args:
        model: Mô hình autoencoder đã train
        val_images: Tập validation
        num_samples: Số lượng mẫu hiển thị
    """
    # Lấy một số mẫu từ tập validation
    indices = np.random.randint(0, len(val_images), num_samples)
    samples = val_images[indices]

    # Dự đoán (tái tạo)
    reconstructions = model.predict(samples)

    # Hiển thị kết quả
    plt.figure(figsize=(15, 4))
    for i in range(num_samples):
        # Ảnh gốc
        plt.subplot(3, num_samples, i + 1)
        plt.imshow(samples[i].reshape(samples.shape[1], samples.shape[2]), cmap='gray')
        plt.title('Ảnh gốc')
        plt.axis('off')

        # Ảnh tái tạo
        plt.subplot(3, num_samples, i + 1 + num_samples)
        plt.imshow(reconstructions[i].reshape(reconstructions.shape[1], reconstructions.shape[2]), cmap='gray')
        plt.title('Ảnh tái tạo')
        plt.axis('off')

        # Sự khác biệt
        plt.subplot(3, num_samples, i + 1 + 2*num_samples)
        diff = np.abs(samples[i] - reconstructions[i]).reshape(samples.shape[1], samples.shape[2])
        plt.imshow(diff, cmap='jet')
        plt.title('Sự khác biệt')
        plt.axis('off')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'reconstruction_results.png'))
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='Huấn luyện Autoencoder để phát hiện bất thường trong ảnh X-quang ngực')
    parser.add_argument('--data_dir', type=str, default='c:\\\\DeepLearning\\\\AE_Xray\\\\backend\\\\dataset', help="Đường dẫn đến thư mục cha chứa 'train' và 'test'")
    parser.add_argument('--img_size', type=int, default=128, help='Kích thước ảnh')
    parser.add_argument('--batch_size', type=int, default=16, help='Kích thước batch (giảm cho model phức tạp)')
    parser.add_argument('--epochs', type=int, default=100, help='Số epoch (tăng cao cho hiệu suất 80%)')
    parser.add_argument('--output_dir', type=str, default='./saved_model', help='Thư mục lưu mô hình')

    args = parser.parse_args()

    # Tạo thư mục đầu ra nếu chưa tồn tại
    os.makedirs(args.output_dir, exist_ok=True)

    # Tải và tiền xử lý dữ liệu
    train_generator, val_generator, val_images = load_and_preprocess_data(
        args.data_dir,
        img_size=args.img_size,
        batch_size=args.batch_size
    )

    if train_generator is None:
        print("Thoát do không tìm thấy dữ liệu huấn luyện.")
        return

    # Xây dựng mô hình
    autoencoder = build_autoencoder(input_shape=(args.img_size, args.img_size, 1))
    print("Đang bắt đầu huấn luyện mô hình...")

    # Lấy callbacks
    callbacks = get_callbacks(os.path.join(args.output_dir, 'autoencoder.h5'))

    # Huấn luyện mô hình
    history = autoencoder.fit(
        train_generator,
        steps_per_epoch=len(train_generator),
        epochs=args.epochs,
        validation_data=val_generator,
        validation_steps=len(val_generator),
        callbacks=callbacks
    )

    # Tải mô hình tốt nhất
    autoencoder.load_weights(os.path.join(args.output_dir, 'autoencoder.h5'))

    # Tính toán ngưỡng
    threshold = calculate_reconstruction_threshold(autoencoder, val_images)
    print(f"Ngưỡng phát hiện bất thường: {threshold:.6f}")

    # Lưu ngưỡng
    with open(os.path.join(args.output_dir, 'threshold.txt'), 'w') as f:
        f.write(str(threshold))

    # Hiển thị kết quả
    visualize_results(autoencoder, val_images, args.output_dir)

    # Vẽ biểu đồ lịch sử huấn luyện
    plt.figure(figsize=(12, 4))
    plt.subplot(1, 2, 1)
    plt.plot(history.history['loss'], label='Loss huấn luyện')
    plt.plot(history.history['val_loss'], label='Loss validation')
    plt.title('Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()

    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, 'training_history.png'))
    plt.close()

    print(f"Mô hình đã lưu tại: {os.path.join(args.output_dir, 'autoencoder.h5')}")
    print(f"Ngưỡng đã lưu tại: {os.path.join(args.output_dir, 'threshold.txt')}")

if __name__ == '__main__':
    main()
